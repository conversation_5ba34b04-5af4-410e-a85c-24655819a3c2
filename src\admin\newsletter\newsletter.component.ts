import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { AccountService } from '../../Utils/_services/account.service';
import { PageTitleService } from '../../Utils/_services/page-title.service';

@Component({
  selector: 'app-newsletter',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './newsletter.component.html',
  styleUrl: './newsletter.component.css'
})
export class NewsletterComponent implements OnInit {
  subscribers: any[] = [];
  totalCount: number = 0;
  pageNumber: number = 1;
  pageSize: number = 10;
  totalPages: number = 1;
  isLoading: boolean = false;
  errorMessage: string = '';

  constructor(
    private accountService: AccountService,
    private pageTitleService: PageTitleService
  ) {}

  ngOnInit(): void {
    this.pageTitleService.setTitle('Newsletter Subscribers');
    this.loadSubscribers();
  }

  loadSubscribers(): void {
    this.isLoading = true;
    this.errorMessage = '';

    this.accountService.GetAllUser(this.pageNumber, this.pageSize).subscribe({
      next: (response) => {
        // Filter only users who have subscribed to newsletter
        this.subscribers = (response.items || []).filter((user: any) => user.newsletter === true);
        this.totalCount = this.subscribers.length;
        this.totalPages = Math.ceil(this.totalCount / this.pageSize);
        this.isLoading = false;
      },
      error: (err) => {
        console.error('Failed to fetch newsletter subscribers', err);
        this.errorMessage = 'Failed to load newsletter subscribers. Please try again later.';
        this.isLoading = false;
      }
    });
  }

  // Pagination methods
  nextPage(): void {
    if (this.pageNumber < this.totalPages) {
      this.pageNumber++;
      this.loadSubscribers();
    }
  }

  prevPage(): void {
    if (this.pageNumber > 1) {
      this.pageNumber--;
      this.loadSubscribers();
    }
  }

  goToPage(page: number): void {
    if (page >= 1 && page <= this.totalPages && page !== this.pageNumber) {
      this.pageNumber = page;
      this.loadSubscribers();
    }
  }

  onPageSizeChange(): void {
    this.pageNumber = 1;
    this.loadSubscribers();
  }

  // Helper methods
  getPaginationArray(): number[] {
    const maxVisible = 5;
    const start = Math.max(1, this.pageNumber - Math.floor(maxVisible / 2));
    const end = Math.min(this.totalPages, start + maxVisible - 1);
    return Array.from({ length: end - start + 1 }, (_, i) => start + i);
  }

  getStartIndex(): number {
    return (this.pageNumber - 1) * this.pageSize + 1;
  }

  getEndIndex(): number {
    return Math.min(this.pageNumber * this.pageSize, this.totalCount);
  }

  formatDate(date: Date | string): string {
    return new Date(date).toLocaleDateString();
  }

  // CRUD operations
  createNewsletter(): void {
    if (this.isSubmitting) return;

    this.isSubmitting = true;
    this.errorMessage = '';

    this.newsletterService.create(this.currentNewsletter).subscribe({
      next: () => {
        this.loadNewsletters();
        this.closeModals();
      },
      error: (err) => {
        console.error('Failed to create newsletter:', err);
        this.errorMessage = 'Failed to create newsletter. Please try again.';
        this.isSubmitting = false;
      }
    });
  }

  updateNewsletter(): void {
    if (this.isSubmitting || !this.currentNewsletter.id) return;

    this.isSubmitting = true;
    this.errorMessage = '';

    this.newsletterService.update(this.currentNewsletter.id, this.currentNewsletter).subscribe({
      next: () => {
        this.loadNewsletters();
        this.closeModals();
      },
      error: (err) => {
        console.error('Failed to update newsletter:', err);
        this.errorMessage = 'Failed to update newsletter. Please try again.';
        this.isSubmitting = false;
      }
    });
  }

  deleteNewsletter(): void {
    if (this.isSubmitting || !this.selectedNewsletter?.id) return;

    this.isSubmitting = true;
    this.errorMessage = '';

    this.newsletterService.delete(this.selectedNewsletter.id).subscribe({
      next: () => {
        this.loadNewsletters();
        this.closeModals();
      },
      error: (err) => {
        console.error('Failed to delete newsletter:', err);
        this.errorMessage = 'Failed to delete newsletter. Please try again.';
        this.isSubmitting = false;
      }
    });
  }

  togglePublishStatus(newsletter: Newsletter): void {
    if (!newsletter.id) return;

    const action = newsletter.isPublished ? 'unpublish' : 'publish';
    const service = newsletter.isPublished
      ? this.newsletterService.unpublish(newsletter.id)
      : this.newsletterService.publish(newsletter.id);

    service.subscribe({
      next: () => {
        newsletter.isPublished = !newsletter.isPublished;
        console.log(`Newsletter ${action}ed successfully`);
      },
      error: (err) => {
        console.error(`Failed to ${action} newsletter:`, err);
        this.errorMessage = `Failed to ${action} newsletter. Please try again.`;
      }
    });
  }

  sendNewsletter(newsletter: Newsletter): void {
    if (!newsletter.id || !newsletter.isPublished) return;

    this.newsletterService.sendNewsletter(newsletter.id).subscribe({
      next: () => {
        console.log('Newsletter sent successfully');
        // You might want to show a success message here
      },
      error: (err) => {
        console.error('Failed to send newsletter:', err);
        this.errorMessage = 'Failed to send newsletter. Please try again.';
      }
    });
  }

  // Helper methods
  getPaginationArray(): number[] {
    const maxVisible = 5;
    const start = Math.max(1, this.pageNumber - Math.floor(maxVisible / 2));
    const end = Math.min(this.totalPages, start + maxVisible - 1);
    return Array.from({ length: end - start + 1 }, (_, i) => start + i);
  }

  getStartIndex(): number {
    return (this.pageNumber - 1) * this.pageSize + 1;
  }

  getEndIndex(): number {
    return Math.min(this.pageNumber * this.pageSize, this.totalCount);
  }

  formatDate(date: Date | string): string {
    return new Date(date).toLocaleDateString();
  }

  truncateText(text: string, maxLength: number = 100): string {
    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
  }
