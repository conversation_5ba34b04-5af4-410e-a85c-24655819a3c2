import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { NewsletterService, Newsletter } from '../../services/newsletter.service';
import { PageTitleService } from '../../Utils/_services/page-title.service';
import { PagedResult } from '../../models/bug-report.model';

@Component({
  selector: 'app-newsletter',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './newsletter.component.html',
  styleUrl: './newsletter.component.css'
})
export class NewsletterComponent implements OnInit {
  newsletters: Newsletter[] = [];
  totalCount: number = 0;
  pageNumber: number = 1;
  pageSize: number = 10;
  totalPages: number = 1;
  isLoading: boolean = false;
  errorMessage: string = '';

  // Modal states
  showCreateModal: boolean = false;
  showEditModal: boolean = false;
  showDeleteModal: boolean = false;
  showPreviewModal: boolean = false;

  // Current newsletter for operations
  currentNewsletter: Newsletter = this.getEmptyNewsletter();
  selectedNewsletter: Newsletter | null = null;

  // Form states
  isSubmitting: boolean = false;

  constructor(
    private newsletterService: NewsletterService,
    private pageTitleService: PageTitleService
  ) {}

  ngOnInit(): void {
    this.pageTitleService.setTitle('Newsletter Management');
    this.loadNewsletters();
  }

  getEmptyNewsletter(): Newsletter {
    return {
      title: '',
      content: '',
      summary: '',
      authorName: '',
      publishDate: new Date(),
      isPublished: false,
      tags: [],
      imageUrl: ''
    };
  }

  loadNewsletters(): void {
    this.isLoading = true;
    this.errorMessage = '';

    this.newsletterService.getAll(this.pageNumber, this.pageSize).subscribe({
      next: (response: PagedResult<Newsletter>) => {
        this.newsletters = response.items || [];
        this.totalCount = response.totalCount || 0;
        this.totalPages = Math.ceil(this.totalCount / this.pageSize);
        this.isLoading = false;
      },
      error: (err) => {
        console.error('Failed to fetch newsletters', err);
        this.errorMessage = 'Failed to load newsletters. Please try again later.';
        this.isLoading = false;
      }
    });
  }

  // Pagination methods
  nextPage(): void {
    if (this.pageNumber < this.totalPages) {
      this.pageNumber++;
      this.loadNewsletters();
    }
  }

  prevPage(): void {
    if (this.pageNumber > 1) {
      this.pageNumber--;
      this.loadNewsletters();
    }
  }

  goToPage(page: number): void {
    if (page >= 1 && page <= this.totalPages && page !== this.pageNumber) {
      this.pageNumber = page;
      this.loadNewsletters();
    }
  }

  onPageSizeChange(): void {
    this.pageNumber = 1;
    this.loadNewsletters();
  }

  // Modal methods
  openCreateModal(): void {
    this.currentNewsletter = this.getEmptyNewsletter();
    this.showCreateModal = true;
  }

  openEditModal(newsletter: Newsletter): void {
    this.currentNewsletter = { ...newsletter };
    this.showEditModal = true;
  }

  openDeleteModal(newsletter: Newsletter): void {
    this.selectedNewsletter = newsletter;
    this.showDeleteModal = true;
  }

  openPreviewModal(newsletter: Newsletter): void {
    this.selectedNewsletter = newsletter;
    this.showPreviewModal = true;
  }

  closeModals(): void {
    this.showCreateModal = false;
    this.showEditModal = false;
    this.showDeleteModal = false;
    this.showPreviewModal = false;
    this.selectedNewsletter = null;
    this.isSubmitting = false;
  }

  // CRUD operations
  createNewsletter(): void {
    if (this.isSubmitting) return;

    this.isSubmitting = true;
    this.errorMessage = '';

    this.newsletterService.create(this.currentNewsletter).subscribe({
      next: () => {
        this.loadNewsletters();
        this.closeModals();
      },
      error: (err) => {
        console.error('Failed to create newsletter:', err);
        this.errorMessage = 'Failed to create newsletter. Please try again.';
        this.isSubmitting = false;
      }
    });
  }

  updateNewsletter(): void {
    if (this.isSubmitting || !this.currentNewsletter.id) return;

    this.isSubmitting = true;
    this.errorMessage = '';

    this.newsletterService.update(this.currentNewsletter.id, this.currentNewsletter).subscribe({
      next: () => {
        this.loadNewsletters();
        this.closeModals();
      },
      error: (err) => {
        console.error('Failed to update newsletter:', err);
        this.errorMessage = 'Failed to update newsletter. Please try again.';
        this.isSubmitting = false;
      }
    });
  }

  deleteNewsletter(): void {
    if (this.isSubmitting || !this.selectedNewsletter?.id) return;

    this.isSubmitting = true;
    this.errorMessage = '';

    this.newsletterService.delete(this.selectedNewsletter.id).subscribe({
      next: () => {
        this.loadNewsletters();
        this.closeModals();
      },
      error: (err) => {
        console.error('Failed to delete newsletter:', err);
        this.errorMessage = 'Failed to delete newsletter. Please try again.';
        this.isSubmitting = false;
      }
    });
  }

  togglePublishStatus(newsletter: Newsletter): void {
    if (!newsletter.id) return;

    const action = newsletter.isPublished ? 'unpublish' : 'publish';
    const service = newsletter.isPublished
      ? this.newsletterService.unpublish(newsletter.id)
      : this.newsletterService.publish(newsletter.id);

    service.subscribe({
      next: () => {
        newsletter.isPublished = !newsletter.isPublished;
        console.log(`Newsletter ${action}ed successfully`);
      },
      error: (err) => {
        console.error(`Failed to ${action} newsletter:`, err);
        this.errorMessage = `Failed to ${action} newsletter. Please try again.`;
      }
    });
  }

  sendNewsletter(newsletter: Newsletter): void {
    if (!newsletter.id || !newsletter.isPublished) return;

    this.newsletterService.sendNewsletter(newsletter.id).subscribe({
      next: () => {
        console.log('Newsletter sent successfully');
        // You might want to show a success message here
      },
      error: (err) => {
        console.error('Failed to send newsletter:', err);
        this.errorMessage = 'Failed to send newsletter. Please try again.';
      }
    });
  }

  // Helper methods
  getPaginationArray(): number[] {
    const maxVisible = 5;
    const start = Math.max(1, this.pageNumber - Math.floor(maxVisible / 2));
    const end = Math.min(this.totalPages, start + maxVisible - 1);
    return Array.from({ length: end - start + 1 }, (_, i) => start + i);
  }

  getStartIndex(): number {
    return (this.pageNumber - 1) * this.pageSize + 1;
  }

  getEndIndex(): number {
    return Math.min(this.pageNumber * this.pageSize, this.totalCount);
  }

  formatDate(date: Date | string): string {
    return new Date(date).toLocaleDateString();
  }

  truncateText(text: string, maxLength: number = 100): string {
    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
  }
