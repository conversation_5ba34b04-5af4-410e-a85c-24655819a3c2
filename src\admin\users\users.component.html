<div class="container-fluid">
  <!-- Header Section -->
  <div class="d-flex justify-content-between align-items-center mb-4">
    <div>
      <h2 class="mb-1 fw-bold">Manage Users</h2>
      <p class="text-muted mb-0">View and manage user accounts</p>
    </div>
    <div class="d-flex gap-2 align-items-center">
      <!-- View Toggle -->
      <div class="btn-group" role="group">
        <button
          type="button"
          class="btn"
          [class.btn-primary]="viewMode === 'list'"
          [class.btn-outline-primary]="viewMode !== 'list'"
          (click)="viewMode = 'list'">
          <i class="bi bi-list-ul me-1"></i>List
        </button>
        <button
          type="button"
          class="btn"
          [class.btn-primary]="viewMode === 'table'"
          [class.btn-outline-primary]="viewMode !== 'table'"
          (click)="viewMode = 'table'">
          <i class="bi bi-table me-1"></i>Table
        </button>
      </div>

      <!-- Page Size Selector -->
      <select
        class="form-select"
        style="width: auto;"
        [(ngModel)]="pageSize"
        (change)="onPageSizeChange()">
        <option value="5">5 per page</option>
        <option value="10">10 per page</option>
        <option value="25">25 per page</option>
        <option value="50">50 per page</option>
      </select>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="isLoading" class="text-center py-5">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
    <p class="mt-2 text-muted">Loading users...</p>
  </div>

  <!-- Error State -->
  <div *ngIf="errorMessage && !isLoading" class="alert alert-danger" role="alert">
    <i class="bi bi-exclamation-triangle me-2"></i>
    {{ errorMessage }}
    <button class="btn btn-sm btn-outline-danger ms-2" (click)="loadUsers()">
      <i class="bi bi-arrow-clockwise me-1"></i>Retry
    </button>
  </div>

  <!-- Users Content -->
  <div *ngIf="!isLoading && !errorMessage">
    <!-- Results Info -->
    <div class="d-flex justify-content-between align-items-center mb-3">
      <div class="text-muted">
        <small>
          Showing {{ getStartIndex() }} to {{ getEndIndex() }} of {{ totalCount }} users
        </small>
      </div>
    </div>

    <!-- List View -->
    <div *ngIf="viewMode === 'list'" class="user-list">
      <div *ngFor="let user of users; let i = index" class="user-card mb-3">
        <div class="d-flex align-items-center p-3">
          <!-- Avatar -->
          <div class="avatar-circle me-3">
            {{ (user.firstName || user.userName)?.charAt(0)?.toUpperCase() || 'U' }}
          </div>

          <!-- User Info -->
          <div class="flex-grow-1">
            <div class="row">
              <div class="col-md-3">
                <h6 class="mb-1 fw-semibold">{{ user.firstName || 'N/A' }} {{ user.lastName || '' }}</h6>
                <small class="text-muted">{{ '@' + (user.userName || 'N/A') }}</small>
              </div>
              <div class="col-md-3">
                <div class="text-muted small">Email</div>
                <div>{{ user.email || 'N/A' }}</div>
              </div>
              <div class="col-md-2">
                <div class="text-muted small">Phone</div>
                <div>{{ user.phone || 'N/A' }}</div>
              </div>
              <div class="col-md-2">
                <div class="text-muted small">Newsletter</div>
                <div class="form-check form-switch">
                  <input
                    class="form-check-input"
                    type="checkbox"
                    [checked]="user.newsletter"
                    (change)="toggleNewsletter(user)"
                    [id]="'newsletter-' + user.id">
                  <label class="form-check-label" [for]="'newsletter-' + user.id">
                    {{ user.newsletter ? 'Yes' : 'No' }}
                  </label>
                </div>
              </div>
              <div class="col-md-2 text-end">
                <div class="btn-group btn-group-sm" role="group">
                  <button type="button" class="btn btn-outline-primary" title="View Details">
                    <i class="bi bi-eye"></i>
                  </button>
                  <button type="button" class="btn btn-outline-warning" title="Edit User">
                    <i class="bi bi-pencil"></i>
                  </button>
                  <button type="button" class="btn btn-outline-danger" title="Delete User">
                    <i class="bi bi-trash"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Table View -->
    <div *ngIf="viewMode === 'table'" class="table-responsive">
      <table class="table table-hover align-middle">
        <thead class="table-light">
          <tr>
            <th scope="col">#</th>
            <th scope="col">User</th>
            <th scope="col">Email</th>
            <th scope="col">Phone</th>
            <th scope="col">Newsletter</th>
            <th scope="col">Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let user of users; let i = index">
            <td>{{ getStartIndex() + i }}</td>
            <td>
              <div class="d-flex align-items-center">
                <div class="avatar-circle me-2">
                  {{ (user.firstName || user.userName)?.charAt(0)?.toUpperCase() || 'U' }}
                </div>
                <div>
                  <div class="fw-semibold">{{ user.firstName || 'N/A' }} {{ user.lastName || '' }}</div>
                  <small class="text-muted">{{ '@' + (user.userName || 'N/A') }}</small>
                </div>
              </div>
            </td>
            <td>{{ user.email || 'N/A' }}</td>
            <td>{{ user.phone || 'N/A' }}</td>
            <td>
              <div class="form-check form-switch">
                <input
                  class="form-check-input"
                  type="checkbox"
                  [checked]="user.newsletter"
                  (change)="toggleNewsletter(user)"
                  [id]="'newsletter-table-' + user.id">
                <label class="form-check-label" [for]="'newsletter-table-' + user.id">
                  {{ user.newsletter ? 'Yes' : 'No' }}
                </label>
              </div>
            </td>
            <td>
              <div class="btn-group btn-group-sm" role="group">
                <button type="button" class="btn btn-outline-primary" title="View Details">
                  <i class="bi bi-eye"></i>
                </button>
                <button type="button" class="btn btn-outline-warning" title="Edit User">
                  <i class="bi bi-pencil"></i>
                </button>
                <button type="button" class="btn btn-outline-danger" title="Delete User">
                  <i class="bi bi-trash"></i>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Empty State -->
    <div *ngIf="users.length === 0" class="text-center py-5">
      <i class="bi bi-people display-1 text-muted"></i>
      <h4 class="mt-3">No Users Found</h4>
      <p class="text-muted">There are no users to display at the moment.</p>
    </div>

    <!-- Pagination -->
    <nav *ngIf="totalPages > 1" aria-label="Users pagination" class="mt-4">
      <ul class="pagination justify-content-center">
        <!-- Previous Button -->
        <li class="page-item" [class.disabled]="pageNumber === 1">
          <button class="page-link" (click)="prevPage()" [disabled]="pageNumber === 1">
            <i class="bi bi-chevron-left"></i>
            Previous
          </button>
        </li>

        <!-- Page Numbers -->
        <li *ngFor="let page of getPaginationArray()"
            class="page-item"
            [class.active]="page === pageNumber">
          <button class="page-link" (click)="goToPage(page)">
            {{ page }}
          </button>
        </li>

        <!-- Next Button -->
        <li class="page-item" [class.disabled]="pageNumber === totalPages">
          <button class="page-link" (click)="nextPage()" [disabled]="pageNumber === totalPages">
            Next
            <i class="bi bi-chevron-right"></i>
          </button>
        </li>
      </ul>
    </nav>
  </div>
</div>
