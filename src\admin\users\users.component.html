<div class="container-fluid">
  <!-- Header Section -->
  <div class="d-flex justify-content-between align-items-center mb-4">
    <div>
      <h2 class="mb-1">Manage Users</h2>
      <p class="text-muted mb-0">View and manage user accounts</p>
    </div>
    <div class="d-flex gap-2">
      <!-- View Toggle -->
      <button
        class="btn btn-outline-secondary"
        (click)="toggleView()"
        [title]="viewMode === 'table' ? 'Switch to Card View' : 'Switch to Table View'">
        <i class="bi" [ngClass]="viewMode === 'table' ? 'bi-grid-3x3-gap' : 'bi-table'"></i>
      </button>

      <!-- Page Size Selector -->
      <select
        class="form-select"
        style="width: auto;"
        [(ngModel)]="pageSize"
        (change)="onPageSizeChange()">
        <option value="5">5 per page</option>
        <option value="10">10 per page</option>
        <option value="25">25 per page</option>
        <option value="50">50 per page</option>
      </select>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="isLoading" class="text-center py-5">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
    <p class="mt-2 text-muted">Loading users...</p>
  </div>

  <!-- Error State -->
  <div *ngIf="errorMessage && !isLoading" class="alert alert-danger" role="alert">
    <i class="bi bi-exclamation-triangle me-2"></i>
    {{ errorMessage }}
    <button class="btn btn-sm btn-outline-danger ms-2" (click)="loadUsers()">
      <i class="bi bi-arrow-clockwise me-1"></i>Retry
    </button>
  </div>

  <!-- Users Content -->
  <div *ngIf="!isLoading && !errorMessage">
    <!-- Results Info -->
    <div class="d-flex justify-content-between align-items-center mb-3">
      <div class="text-muted">
        <small>
          Showing {{ getStartIndex() }} to {{ getEndIndex() }} of {{ totalCount }} users
        </small>
      </div>
    </div>

    <!-- Table View -->
    <div *ngIf="viewMode === 'table'" class="table-responsive">
      <table class="table table-hover">
        <thead class="table-light">
          <tr>
            <th scope="col">#</th>
            <th scope="col">ID</th>
            <th scope="col">Username</th>
            <th scope="col">Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let user of users; let i = index">
            <td>{{ getStartIndex() + i }}</td>
            <td>{{ user.id }}</td>
            <td>
              <div class="d-flex align-items-center">
                <div class="avatar-circle me-2">
                  {{ user.userName?.charAt(0)?.toUpperCase() || 'U' }}
                </div>
                {{ user.userName }}
              </div>
            </td>
            <td>
              <div class="btn-group btn-group-sm" role="group">
                <button type="button" class="btn btn-outline-primary" title="View Details">
                  <i class="bi bi-eye"></i>
                </button>
                <button type="button" class="btn btn-outline-warning" title="Edit User">
                  <i class="bi bi-pencil"></i>
                </button>
                <button type="button" class="btn btn-outline-danger" title="Delete User">
                  <i class="bi bi-trash"></i>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Card View -->
    <div *ngIf="viewMode === 'card'" class="row">
      <div *ngFor="let user of users; let i = index" class="col-md-6 col-lg-4 mb-3">
        <div class="card h-100">
          <div class="card-body">
            <div class="d-flex align-items-center mb-3">
              <div class="avatar-circle me-3">
                {{ user.userName?.charAt(0)?.toUpperCase() || 'U' }}
              </div>
              <div>
                <h6 class="card-title mb-1">{{ user.userName }}</h6>
                <small class="text-muted">ID: {{ user.id }}</small>
              </div>
            </div>
            <div class="d-flex gap-1">
              <button type="button" class="btn btn-sm btn-outline-primary flex-fill">
                <i class="bi bi-eye me-1"></i>View
              </button>
              <button type="button" class="btn btn-sm btn-outline-warning flex-fill">
                <i class="bi bi-pencil me-1"></i>Edit
              </button>
              <button type="button" class="btn btn-sm btn-outline-danger flex-fill">
                <i class="bi bi-trash me-1"></i>Delete
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div *ngIf="users.length === 0" class="text-center py-5">
      <i class="bi bi-people display-1 text-muted"></i>
      <h4 class="mt-3">No Users Found</h4>
      <p class="text-muted">There are no users to display at the moment.</p>
    </div>

    <!-- Pagination -->
    <nav *ngIf="totalPages > 1" aria-label="Users pagination" class="mt-4">
      <ul class="pagination justify-content-center">
        <!-- Previous Button -->
        <li class="page-item" [class.disabled]="pageNumber === 1">
          <button class="page-link" (click)="prevPage()" [disabled]="pageNumber === 1">
            <i class="bi bi-chevron-left"></i>
            Previous
          </button>
        </li>

        <!-- Page Numbers -->
        <li *ngFor="let page of getPaginationArray()"
            class="page-item"
            [class.active]="page === pageNumber">
          <button class="page-link" (click)="goToPage(page)">
            {{ page }}
          </button>
        </li>

        <!-- Next Button -->
        <li class="page-item" [class.disabled]="pageNumber === totalPages">
          <button class="page-link" (click)="nextPage()" [disabled]="pageNumber === totalPages">
            Next
            <i class="bi bi-chevron-right"></i>
          </button>
        </li>
      </ul>
    </nav>
  </div>
</div>
