/* Avatar Circle */
.avatar-circle {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 18px;
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(79, 70, 229, 0.3);
}



/* Table Styling */
.table {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.table thead th {
  background: #f8fafc;
  border-bottom: 2px solid #e5e7eb;
  font-weight: 600;
  color: #374151;
  padding: 1rem;
}

.table tbody td {
  padding: 1rem;
  vertical-align: middle;
  border-bottom: 1px solid #f3f4f6;
}

.table tbody tr:last-child td {
  border-bottom: none;
}

/* Table hover effects */
.table tbody tr {
  transition: background-color 0.2s ease-in-out;
}

.table tbody tr:hover {
  background-color: #f9fafb;
}

/* Newsletter Toggle Styling */
.form-check-input {
  width: 2.5rem;
  height: 1.25rem;
  border-radius: 1rem;
  background-color: #e5e7eb;
  border: none;
  transition: all 0.2s ease-in-out;
}

.form-check-input:checked {
  background-color: #10b981;
  border-color: #10b981;
}

.form-check-input:focus {
  box-shadow: 0 0 0 0.2rem rgba(16, 185, 129, 0.25);
}

.form-check-label {
  font-size: 0.875rem;
  font-weight: 500;
  margin-left: 0.5rem;
}

/* Button group styling */
.btn-group-sm .btn {
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
}

.btn-outline-primary {
  color: #4f46e5;
  border-color: #4f46e5;
}

.btn-outline-primary:hover {
  background-color: #4f46e5;
  border-color: #4f46e5;
}

.btn-outline-warning {
  color: #f59e0b;
  border-color: #f59e0b;
}

.btn-outline-warning:hover {
  background-color: #f59e0b;
  border-color: #f59e0b;
}

.btn-outline-danger {
  color: #ef4444;
  border-color: #ef4444;
}

.btn-outline-danger:hover {
  background-color: #ef4444;
  border-color: #ef4444;
}

/* Loading spinner container */
.spinner-border {
  width: 3rem;
  height: 3rem;
}

/* Pagination styling */
.pagination .page-link {
  color: #495057;
  border-color: #dee2e6;
  transition: all 0.2s ease-in-out;
}

.pagination .page-link:hover {
  color: #0056b3;
  background-color: #e9ecef;
  border-color: #dee2e6;
}

.pagination .page-item.active .page-link {
  background-color: #007bff;
  border-color: #007bff;
}

.pagination .page-item.disabled .page-link {
  color: #6c757d;
  background-color: #fff;
  border-color: #dee2e6;
}



/* Form select styling */
.form-select {
  border-color: #ced4da;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-select:focus {
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Alert styling */
.alert {
  border: none;
  border-radius: 8px;
}

.alert-danger {
  background-color: #f8d7da;
  color: #721c24;
}

/* Empty state styling */
.display-1 {
  font-size: 4rem;
  opacity: 0.3;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .d-flex.justify-content-between {
    flex-direction: column;
    gap: 1rem;
  }

  .btn-group-sm .btn {
    padding: 0.2rem 0.4rem;
    font-size: 0.8rem;
  }

  .avatar-circle {
    width: 32px;
    height: 32px;
    font-size: 14px;
  }

  .table-responsive {
    font-size: 0.9rem;
  }
}

/* Custom scrollbar for table */
.table-responsive::-webkit-scrollbar {
  height: 8px;
}

.table-responsive::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}