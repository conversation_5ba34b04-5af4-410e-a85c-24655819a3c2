import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { PagedResult } from '../models/bug-report.model';

export interface Newsletter {
  id?: string;
  title: string;
  content: string;
  summary?: string;
  authorName: string;
  publishDate: Date;
  isPublished: boolean;
  tags?: string[];
  imageUrl?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

@Injectable({
  providedIn: 'root'
})
export class NewsletterService {
  private baseUrl = 'http://localhost:5001/api/newsletter';

  constructor(private http: HttpClient) {}

  getAll(pageNumber: number, pageSize: number): Observable<PagedResult<Newsletter>> {
    const params = new HttpParams()
      .set('pageNumber', pageNumber.toString())
      .set('pageSize', pageSize.toString());
    return this.http.get<PagedResult<Newsletter>>(this.baseUrl, { params });
  }

  getById(id: string): Observable<Newsletter> {
    return this.http.get<Newsletter>(`${this.baseUrl}/${id}`);
  }

  create(newsletter: Newsletter): Observable<Newsletter> {
    return this.http.post<Newsletter>(this.baseUrl, newsletter);
  }

  update(id: string, newsletter: Newsletter): Observable<Newsletter> {
    return this.http.put<Newsletter>(`${this.baseUrl}/${id}`, newsletter);
  }

  delete(id: string): Observable<void> {
    return this.http.delete<void>(`${this.baseUrl}/${id}`);
  }

  publish(id: string): Observable<void> {
    return this.http.patch<void>(`${this.baseUrl}/${id}/publish`, {});
  }

  unpublish(id: string): Observable<void> {
    return this.http.patch<void>(`${this.baseUrl}/${id}/unpublish`, {});
  }

  getSubscribers(): Observable<any[]> {
    return this.http.get<any[]>(`${this.baseUrl}/subscribers`);
  }

  sendNewsletter(id: string): Observable<void> {
    return this.http.post<void>(`${this.baseUrl}/${id}/send`, {});
  }
}
