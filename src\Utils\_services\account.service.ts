import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ILoginUser_Request, ILoginUser_Response, IRegisterUser_Request, IUserLogin } from "../../Components/Home/Account/Register/register.component.model";
import { BehaviorSubject, map } from 'rxjs';
import { environment } from '../../Config/environments/environment';

//Created when user browsers to that page and displosed of when user changes page or component is closed
@Injectable({
  providedIn: 'root'
})
export class AccountService {
  private readonly _httpClient: HttpClient;
  private readonly baseURL = environment.apiUrl;
  public currentUserSource = new BehaviorSubject<IUserLogin | null>(null);
  public currentUser$ = this.currentUserSource.asObservable();

  constructor(httpClient: HttpClient) {
    this._httpClient = httpClient;
  }

  login(model: ILoginUser_Request) {
    return this._httpClient.post<IUserLogin>(`${this.baseURL}/account/login`, model).pipe(
      map((response: IUserLogin) => {
        if (response) {
          localStorage.setItem("user", JSON.stringify(response));
          this.currentUserSource.next(response);
        }
        return response;
      })
    );
  }

  onSubmitForm(model: IRegisterUser_Request) {
    // The API returns a user object on successful registration.
    // We'll treat the response as IUserLogin for consistency.
    return this._httpClient.post<IUserLogin>(`${this.baseURL}/account/register`, model).pipe(
      map((response: IUserLogin) => {
        if (response) {
          localStorage.setItem("user", JSON.stringify(response));
          this.currentUserSource.next(response);
        }
        return response; // Pass it along
      })
    );
  }

  //Will be set when the application is started up
  setCurrentUser(user: IUserLogin) {
    this.currentUserSource.next(user);
  }

  logout() {
    localStorage.removeItem("user");
    this.currentUserSource.next(null);
  }
}