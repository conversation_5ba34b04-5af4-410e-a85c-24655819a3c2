import { Routes } from '@angular/router';
import { AppComponent } from "./app.component";
// import { authGuard } from '../Utils/_guards/auth.guard';
import { TestErrorsComponent } from '../Components/Global/errors/test-errors/test-errors.component';
import { NotFoundComponent } from '../Components/Global/errors/not-found/not-found.component';
import { ServerErrorComponent } from '../Components/Global/errors/server-error/server-error.component';
import { RegisterComponent } from '../Components/Home/Account/Register/register.component';
import { BodyComponent } from '../Components/Layout/2.Body/body.component';
import { CareersComponent } from '../Components/careers/careers.component';
import { AboutComponent } from '../Components/about/about.component';
import { ContactComponent } from '../Components/contact/contact.component';
import { BugFixLogComponent } from '../Components/bug-fix-log/bug-fix-log.component';
import { FaqComponent } from '../Components/faq/faq.component';
import { BodyContentItemComponent } from '../Components/Home/Body/body.component';
import { DashboardComponent } from '../admin/dashboard/dashboard.component';
import { AdminLayoutComponent } from '../layouts/admin-layout/admin-layout.component';
import { NavComponent } from '../Components/Layout/1.Nav/nav.component';
import { UserLayoutComponent } from '../layouts/user-layout/user-layout.component';
import { JobPostComponent } from '../admin/job-post/job-post.component';
import { BugReportingComponent } from '../admin/bug-reporting/bug-reporting.component';
import { UsersComponent } from '../admin/users/users.component';
import { NewsletterComponent } from '../admin/newsletter/newsletter.component';
import { AppliedjobsComponent } from '../admin/appliedjobs/appliedjobs.component';
import { AppManagementComponent } from '../admin/app-management/app-management.component';

export const routes: Routes = [
  {
    path: '',
    redirectTo: 'home',
    pathMatch: 'full'
  },

  {
    path: 'home',
    component: UserLayoutComponent,
    children: [
      { path: '', component: BodyContentItemComponent },
      { path: 'register', component: RegisterComponent },
      { path: 'careers', component: CareersComponent },
      { path: 'about', component: AboutComponent },
      { path: 'contact', component: ContactComponent },
      { path: 'bug-logs', component: BugFixLogComponent },
      { path: 'faq', component: FaqComponent }
    ]
  },

  {
    path: 'admin',
    component: AdminLayoutComponent,
    children: [
      { path: 'dashboard', component: DashboardComponent },
      { path: 'app-management', component: AppManagementComponent },
      { path: '', component: DashboardComponent },
      { path: 'job-post', component: JobPostComponent },
      { path: 'bug-reporting', component: BugReportingComponent },
      { path: 'users', component: UsersComponent },
      { path: 'newsletters', component: NewsletterComponent },
      { path: 'appliedjobs', component: AppliedjobsComponent }

    ]
  },

  { path: '**', redirectTo: 'home' }
];


