export interface IAppUsers {
    id: number;
    userName: string;
}

export interface IRegisterUser_Request {
    firstName: string;
    lastName: string;
    email: string;
    username: string;
    password: string;
    dob: string;
    newsletter: boolean;
}

export interface IRegisterUser_Response {};

export interface ILoginUser_Request {
    usernameOrEmail: string;
    password: string;
}

// This interface represents the user object stored in the app and returned by the API.
export interface IUserLogin {
    firstName: string;
    token: string;
}

// This can be an empty interface if the register endpoint returns the same as login.
export type ILoginUser_Response = IUserLogin;